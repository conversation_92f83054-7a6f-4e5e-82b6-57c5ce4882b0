import re
import asyncio
import random
import time
from itertools import cycle
import discord
from discord.utils import get
from discord.ext import commands, tasks

intents = discord.Intents.default()
intents.messages = True
intents.guilds = True
intents.reactions = True
intents.members = True
# message_content intent is only available in discord.py 2.0+
try:
    intents.message_content = True
except AttributeError:
    pass  # Older version of discord.py

TOKEN = "MTE5MDUyMDcwMjMzNjQ5OTczMg.Gm5BRt.hKmicp_F9s_wMKXWFprAzNOmGY_JHDtxKu5ieY"
COMMAND_PREFIX_STRING = "command"  # The prefix to look for at the start of the line
TARGET_BOT_ID_STRING = "examplebotid" # The specific bot ID string to look for
SEND_DELAY = 0.7  # Seconds between each message
REPEAT_THRESHOLD = 200
SMALL_FILE_THRESHOLD = 30
TYPING_TIMEOUT = 30 # 10 minutes in seconds

prefixes = {"default": "+"}  # Default prefix is '+'
current_prefix = prefixes["default"]
autoreact_targets = {}
emoji_list = {}
reply_targets = {}
pending_usernames = {}
reply_words = []
guild_names = []
current_index = 0
afk_users = {}
used_lines = set()
spamming = False  # Flag to control spamming
sped_lines = []
used_comma_parts = set()  # Track used parts from comma-separated lines

zephyr = commands.Bot(command_prefix=commands.when_mentioned_or(prefixes["default"]), self_bot=True, case_insensitive=True, intents=intents)

def load_sped_lines():
    try:
        with open("spedwords.txt", "r", encoding="utf-8") as f:
            return [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        return []

def load_words():
    try:
        with open('gcwords.txt', 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        return []

def load_reply_words():
    try:
        with open("replywords.txt", "r") as file:
            reply_words = [line.strip() for line in file.readlines() if line.strip()]
        return reply_words
    except FileNotFoundError:
        print("Error: replywords.txt not found.")
        return []
    except Exception as e:
        print(f"Error loading reply words: {e}")
        return []

def generate_commands_list():
    current_prefix = prefixes.get("default", "+")
    return f"""
    ```⠀⠀⠀⠀⠀⠀   ⠀⠀⠀⠀⠀⠀⠀⠀⠀```
⠀ ⠀ ⢸⣦⡀⠀⠀⠀⠀⢀⡄⠀⠀⠀⠀⠀⠀⠀⠀
⠀⠀⠀⢸⣏⠻⣶⣤⡶⢾⡿⠁⠀⢠⣄⡀⢀⣴⠀⠀  ⠀
⠀⠀⣀⣼⠷⠀⠀⠁⢀⣿⠃⠀⠀⢀⣿⣿⣿⣇⠀⠀
⠴⣾⣯⣅⣀⠀⠀⠀⠈⢻⣦⡀⠒⠻⠿⣿⡿⠿⠓    ⠀
⠀⠀⠀⠉⢻⡇⣤⣾⣿⣷⣿⣿⣤⠀⠀⣿⠁⠀⠀⠀
⠀⠀⠀⠀⠸⣿⡿⠏⠀          ⠀⠀⠀⠀⠀⠀⠀⠀⠀
⠀⠀⠀⠀⠀⠟⠁
```zephyr v1.0.0⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
``````js
`Prefix:[{current_prefix}]`
Use {current_prefix}prefix [prefix] to change.
``````  > Main
Menu - Shows this command list.
React - User | Emojis 1,2,3..
Reply - User | Optional name
Ladder - Messages
Sped - Sends messages to a specified channel with aggressive rate limit handling and typing indicator.```
```  > Spamming``````Gc - Channel id - Optional name```
```  > Raiding```
```Miscellaneous``````
Av - User```
```  > Stop/Off
Stopreact - Disables react.
Stopreply - Disables reply.
Stopgc - Stops the gc.
Stopsped - Stops the sped spam and typing indicator.``````Use {current_prefix}help [command] for info.```⠀
"""

async def safe_send(channel, message):
    """Sends a message and aggressively handles potential rate limits."""
    while True:
        try:
            await channel.send(message)
            return  # Message sent successfully, exit the loop
        except discord.errors.HTTPException as e:
            if e.status == 429:  # Explicitly check the status code for rate limit
                retry_after = e.retry_after if hasattr(e, 'retry_after') else 1  # Fallback
                print(f"Rate limited (status 429)! Waiting {retry_after:.2f} seconds before retrying.")
                await asyncio.sleep(retry_after)
            elif "TOO_MANY_REQUESTS" in str(e): # Check if the error message contains this
                # Sometimes the status code might not be 429, but the message indicates it
                retry_after_match = re.search(r'retry after (\d+\.\d+) seconds', str(e), re.IGNORECASE)
                if retry_after_match:
                    retry_after = float(retry_after_match.group(1))
                    print(f"Rate limited (message)! Waiting {retry_after:.2f} seconds before retrying.")
                    await asyncio.sleep(retry_after)
                else:
                    print(f"Rate limit indicated in message, but no retry_after found. Waiting 2 seconds.")
                    await asyncio.sleep(2)
            else:
                print(f"HTTPException while sending (not rate limit): {e}")
                await asyncio.sleep(1) # Wait a bit for other HTTP errors
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            await asyncio.sleep(1) # Wait a bit for unexpected errors

async def typing_indicator(channel):
    global spamming
    last_message_time = time.time()
    try:
        while spamming:
            await channel.typing()
            last_message_time = time.time()
            await asyncio.sleep(5)  # Send typing every 5 seconds while spamming
            if not spamming and (time.time() - last_message_time) > TYPING_TIMEOUT:
                break
        print("Typing indicator stopped (timeout or stopped).")
    except asyncio.CancelledError:
        print("Typing indicator stopped (cancelled).")

@zephyr.event
async def on_ready():
    global all_words, sped_lines, reply_words, used_words
    all_words = load_words()
    sped_lines = load_sped_lines()
    reply_words = load_reply_words()
    used_words = set()  # Initialize used_words for gc command
    print(f'Logged in as {zephyr.user} ({zephyr.user.id})')
    # Removed the server loading print statements

@zephyr.event
async def on_message(message):
    # Allow the bot to react and reply to itself if needed
    # Process autoreactions
    if message.author.id in autoreact_targets:
        try:
            # Get the next emoji in the rotation
            next_emoji = next(autoreact_targets[message.author.id])
            await message.add_reaction(next_emoji)
            print(f"Reacted to {message.author.name} with {next_emoji}")
        except discord.HTTPException as e:
            print(f"Failed to react: {e}")
        except Exception as e:
            print(f"Error in autoreact: {e}")

    # Process autoreplies
    if message.author.id in reply_targets and reply_words:
        try:
            reply_message = random.choice(reply_words)
            await message.channel.send(reply_message)
            print(f"Replied to {message.author.name} with: {reply_message}")
        except Exception as e:
            print(f"Error in autoreply: {e}")

    # Process commands
    await zephyr.process_commands(message)

@zephyr.command()
async def menu(ctx):
    print('User ran menu command.')
    await ctx.send(generate_commands_list())

@zephyr.command()
async def prefix(ctx, new_prefix: str):
    print(f'User changed the prefix to {new_prefix}.')
    prefixes["default"] = new_prefix
    zephyr.command_prefix = commands.when_mentioned_or(new_prefix)
    await ctx.send(f"Prefix changed to `{new_prefix}`")

@zephyr.command()
async def react(ctx, user: discord.User, emojis: str):
    print('User ran react command.')
    emoji_list = [emoji.strip() for emoji in emojis.split(',') if emoji.strip()]
    if not emoji_list:
        await ctx.send("Please provide at least one emoji.")
        return
    autoreact_targets[user.id] = cycle(emoji_list)
    await ctx.send(f'Reacting to {user.mention} with the emojis: {", ".join(emoji_list)}')

@zephyr.command()
async def stopreact(ctx):
    print('User ran stopreact command.')
    autoreact_targets.clear()
    await ctx.send("Stopped reacting.")

@zephyr.command()
async def gc(ctx, channel_id: int, target_name: str = None):
    print('User ran gc command.')
    global gc_rename_loop, rename_counter
    channel = zephyr.get_channel(channel_id)

    if not channel:
        await ctx.send("Invalid channel id.")
        return

    if not all_words:
        await ctx.send("Gcwords.txt is empty.")
        return

    await ctx.send(f"Gc changer enabled on '`{channel_id}`'.")

    @tasks.loop(seconds=2.0)
    async def rename_loop():
        global used_words, rename_counter

        # Initialize used_words if not already done
        if 'used_words' not in globals():
            used_words = set()

        if target_name:
            filtered_words = list(set(all_words) - used_words)
        else:
            filtered_words = [w for w in set(all_words) - used_words if "targetname" not in w.lower()]

        if not filtered_words:
            used_words.clear()
            return

        selected = random.choice(filtered_words)
        used_words.add(selected)
        renamed_value = selected.replace("targetname", target_name) if target_name else selected
        renamed_value_with_number = f"{renamed_value} {rename_counter}"

        try:
            await channel.edit(name=renamed_value_with_number)
            print(f"Renamed GC to: {renamed_value_with_number}")
        except Exception as e:
            print(f"Error renaming channel: {e}")

        rename_counter += 1

    rename_counter = 1
    gc_rename_loop = rename_loop
    rename_loop.start()

@zephyr.command()
async def stopgc(ctx):
    print('User stopped gc command.')
    global gc_rename_loop

    if gc_rename_loop and gc_rename_loop.is_running():
        gc_rename_loop.stop()
        await ctx.send("Stopped changing gc name.")
    else:
        await ctx.send("Gc command is not running.")

@zephyr.command()
async def reply(ctx, target: str, *, name: str = None):
    print('User started reply command.')
    global reply_targets, reply_words

    target_id = None

    if target.startswith("<@") and target.endswith(">"):
        target_id = int(target[2:-1]) if target[2] != "!" else int(target[3:-1])
    elif target.isdigit():
        target_id = int(target)
    else:
        user = get(ctx.guild.members, name=target)
        if user:
            target_id = user.id

    if target_id is None:
        await ctx.send("Invalid user.")
        return

    try:
        with open("replywords.txt", "r", encoding="utf-8") as f:
            raw_lines = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        await ctx.send("replywords.txt file not found. Please create it with reply messages.")
        return
    except Exception as e:
        print(f"Error reading file: {e}")
        await ctx.send("Error reading replywords.txt file.")
        return

    if not raw_lines:
        await ctx.send("replywords.txt is empty. Please add some reply messages.")
        return

    if name:
        reply_words = [line.replace("replytarget", name) for line in raw_lines]
    else:
        reply_words = [line for line in raw_lines]

    reply_targets[target_id] = True
    await ctx.send(f"Replying to <@{target_id}> .")

@zephyr.command()
async def stopreply(ctx):
    print('User stopped reply command.')
    global reply_targets

    if not reply_targets:
        await ctx.send("No targets being replied to.")
        return

    reply_targets.clear()
    await ctx.send("Stopped replying.")

@zephyr.command()
async def sped(ctx, target_channel_id: int = None):
    """Sends messages from spedwords.txt to the specified channel
    with aggressive rate limit handling and typing indicator.
    Usage: +sped <channel_id>
    Lines starting with 'command examplebotid ' followed by space-separated words
    will be sent as individual messages to the target channel.
    Lines with commas are split and sent one by one to the target channel (no repeats within threshold).
    Other lines are sent randomly, avoiding repeats within the last 20 messages
    (unless the total number of lines is less than or equal to 30)."""
    global spamming, sped_lines, used_lines, used_comma_parts
    if target_channel_id is None:
        await ctx.send("Please specify a target channel ID.")
        return

    target_channel = zephyr.get_channel(target_channel_id)
    if target_channel is None:
        await ctx.send("Invalid target channel ID.")
        return

    spamming = True
    used_lines = set()
    used_comma_parts = set()  # Track used parts from comma-separated lines
    typing_task_local = None

    if spamming:
        typing_task_local = asyncio.create_task(typing_indicator(target_channel))

    try:
        while spamming:
            if not sped_lines:
                sped_lines = load_sped_lines()  # Reload if empty
                if not sped_lines:
                    await ctx.send("spedwords.txt is empty.")
                    break

            line = random.choice(sped_lines)

            if line.startswith(f"{COMMAND_PREFIX_STRING} {TARGET_BOT_ID_STRING} "):
                command_to_send = line[len(f"{COMMAND_PREFIX_STRING} {TARGET_BOT_ID_STRING} "):].strip()
                words = command_to_send.split()
                for word in words:
                    if not spamming:
                        break
                    if word:
                        await safe_send(target_channel, word)
                        await asyncio.sleep(SEND_DELAY)
            elif "," in line:
                messages = [msg.strip() for msg in line.split(",")]
                for msg in messages:
                    if not spamming:
                        break
                    if len(sped_lines) <= SMALL_FILE_THRESHOLD or msg not in used_comma_parts:
                        await safe_send(target_channel, msg)
                        await asyncio.sleep(SEND_DELAY)
                        if len(sped_lines) > SMALL_FILE_THRESHOLD:
                            used_comma_parts.add(msg)
                            if len(used_comma_parts) > REPEAT_THRESHOLD:
                                if messages:  # Ensure messages list is not empty
                                    oldest_part = messages[0] # Assuming order of sending matters for "oldest"
                                    if oldest_part in used_comma_parts:
                                        used_comma_parts.remove(oldest_part)
                    elif len(sped_lines) > SMALL_FILE_THRESHOLD and msg in used_comma_parts:
                        print(f"Skipping repeated comma-separated part: {msg}")
            else:
                if len(sped_lines) <= SMALL_FILE_THRESHOLD or line not in used_lines:
                    await safe_send(target_channel, line)
                    await asyncio.sleep(SEND_DELAY)
                    if len(sped_lines) > SMALL_FILE_THRESHOLD:
                        used_lines.add(line)
                        if len(used_lines) > REPEAT_THRESHOLD:
                            used_lines_list = list(used_lines)
                            if used_lines_list:  # Ensure the list is not empty
                                used_lines.remove(used_lines_list[0])
                elif len(sped_lines) > SMALL_FILE_THRESHOLD and line in used_lines:
                    print(f"Skipping repeated line: {line}")

            await asyncio.sleep(0.1)  # Small delay to prevent busy-waiting

    except FileNotFoundError:
        await ctx.send("spedwords.txt not found. Make sure it's in the same directory as the bot.")
    except Exception as e:
        await ctx.send(f"An error occurred: {e}")
    finally:
        spamming = False
        if typing_task_local and not typing_task_local.done():
            typing_task_local.cancel()

@zephyr.command()
async def stopsped(ctx):
    """Stops the sped spamming and typing indicator."""
    global spamming
    spamming = False
    await ctx.send("Sped spamming stopped.")
    # The typing indicator will naturally stop due to the 'spamming' flag in the typing_indicator function

@zephyr.command()
async def ladder(ctx, *, messages: str):
    await ctx.message.delete()
    for word in messages.split():
        await ctx.send(word)

@zephyr.command()
async def av(ctx, user: discord.User = None):
    user = user or ctx.author
    try:
        # Try new discord.py 2.0+ method first
        avatar_url = user.avatar.url if user.avatar else user.default_avatar.url
    except AttributeError:
        # Fallback to older discord.py method
        avatar_url = str(user.avatar_url) if hasattr(user, 'avatar_url') else str(user.default_avatar_url)
    await ctx.send(avatar_url)

zephyr.run(TOKEN, bot=False)