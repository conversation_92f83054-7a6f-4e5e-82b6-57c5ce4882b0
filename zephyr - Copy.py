import itertools
from itertools import cycle
import discord
from discord.ext import commands
intents = discord.Intents.default()
intents.messages = True
intents.guilds = True
intents.messages =True

TOKEN = "MTE5MDUyMDcwMjMzNjQ5OTczMg.G36fpM.foYjpCsJxf7wTCGDUES4VJ5BwMScjYsCSmj9Zg"

# Set up intents
intents = discord.Intents.default()

prefixes = {"default": "+"}  # Default prefix is '+'
current_prefix = prefixes["default"]
autoreact_targets = {}

from discord.ext import commands

# Assuming prefixes is a dictionary defined elsewhere
prefixes = {"default": "+"}

zephyr = commands.Bot(command_prefix=commands.when_mentioned_or(prefixes["default"]), self_bot=True)

# Function to generate the command list with the current prefix
def generate_commands_list():
    current_prefix = prefixes.get("default", "+")
    return f"""
## > 𝗭ephyr beta
```js
`Prefix:[{current_prefix}]`
Use {current_prefix}prefix [prefix] to change.
`````` > Main
Menu - Shows this command list.
React - Target | emojis
Stopreact - Stops reacting.
Reply - Target```
"""

@zephyr.event
async def on_ready():
    print(f'Logged in as {zephyr.user}')

@zephyr.command()
async def menu(ctx):
    print('User ran menu command.')  # Debugging line
    await ctx.send(generate_commands_list())

@zephyr.command()
async def prefix(ctx, new_prefix: str):
    prefixes["default"] = new_prefix
    zephyr.command_prefix = commands.when_mentioned_or(new_prefix)
    
    # Confirm the change
    await ctx.send(f"Prefix has been changed to `{new_prefix}`.")

@zephyr.event
async def on_message(message):
    if message.author == zephyr.user:
        return
    
    # Get the current global prefix
    prefix = prefixes.get("default", "+")
    
    # Only process commands if the message starts with the configured prefix
    if message.content.startswith(prefix):
        await zephyr.process_commands(message)

@zephyr.command()
async def react(ctx, user: discord.User, emojis: str):
    print('User ran react command.')
    
    # Split the emojis by comma and strip any extra spaces
    emoji_list = [emoji.strip() for emoji in emojis.split(',')]
    
    # Use cycle to create an infinite loop over the emoji list
    autoreact_targets[user.id] = cycle(emoji_list)
    
    await ctx.send(f'Reacting to {user.mention} with the emojis: {", ".join(emoji_list)}')

@zephyr.event
async def on_message(message):
    if message.author.id in autoreact_targets and not message.author.bot:
        try:
            # Get the next emoji in the rotation
            next_emoji = next(autoreact_targets[message.author.id])
            await message.add_reaction(next_emoji)
        except discord.HTTPException:
            pass  # Invalid emoji or other error
    await zephyr.process_commands(message)

@zephyr.command()
async def stopreact(ctx):
    print('User ran stopreact command.')
    # Clear all reactions stored in autoreact_targets
    autoreact_targets.clear()
    await ctx.send("Stopped reacting.")


# Run the bot
zephyr.run(TOKEN, bot=False)